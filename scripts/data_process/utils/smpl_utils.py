import numpy as np
import smplx
import torch
from scipy.spatial.transform import Rotation as R
from smplx.joint_names import JOINT_NAMES
from scipy.interpolate import interp1d


def load_smplx_body_model(smplx_body_model_path):
    body_model = smplx.create(
        smplx_body_model_path,
        "smplx",
        gender="neutral",
        use_pca=False,
    )
    return body_model


def load_smplx_clip(smplx_clip, smplx_body_model):
    smplx_clip = np.load(smplx_clip)
    body_model = smplx_body_model
    
    print("the smplx clip keys ", smplx_clip.keys())
    print(smplx_clip["pose_body"].shape)
    print(smplx_clip["betas"].shape)
    print(smplx_clip["root_orient"].shape)
    print(smplx_clip["trans"].shape)
    
    num_frames = smplx_clip["pose_body"].shape[0]
    human_pose = body_model(
        betas=torch.tensor(smplx_clip["betas"]).float().view(1, -1), # (16,)
        global_orient=torch.tensor(smplx_clip["root_orient"]).float(), # (N, 3)
        body_pose=torch.tensor(smplx_clip["pose_body"]).float(), # (N, 63)
        transl=torch.tensor(smplx_clip["trans"]).float(), # (N, 3)
        left_hand_pose=torch.zeros(num_frames, 45).float(),
        right_hand_pose=torch.zeros(num_frames, 45).float(),
        jaw_pose=torch.zeros(num_frames, 3).float(),
        leye_pose=torch.zeros(num_frames, 3).float(),
        reye_pose=torch.zeros(num_frames, 3).float(),
        return_full_pose=True,
    )
    
    if len(smplx_clip["betas"].shape)==1:
        human_height = 1.66 + 0.1 * smplx_clip["betas"][0]
    else:
        human_height = 1.66 + 0.1 * smplx_clip["betas"][0, 0]
    
    return smplx_clip, body_model, human_pose, human_height


def get_smplx_data_offline_fast(smplx_clip, body_model, human_pose, tgt_fps=30):
    """
    Must return a dictionary with the following structure:
    {
        "Hips": (position, orientation),
        "Spine": (position, orientation),
        ...
    }
    """
    src_fps = smplx_clip["mocap_frame_rate"].item()
    frame_skip = int(src_fps / tgt_fps)
    num_frames = smplx_clip["pose_body"].shape[0]
    global_orient = human_pose.global_orient.squeeze()
    full_body_pose = human_pose.full_pose.reshape(num_frames, -1, 3)
    joints = human_pose.joints.detach().numpy().squeeze()
    joint_names = JOINT_NAMES[: len(body_model.parents)]
    parents = body_model.parents
    aligned_fps = tgt_fps
        
    smplx_motion_frames = []
    for curr_frame in range(len(global_orient)):
        result = {}
        single_global_orient = global_orient[curr_frame]
        single_full_body_pose = full_body_pose[curr_frame]
        single_joints = joints[curr_frame]
        joint_orientations = []
        for i, joint_name in enumerate(joint_names):
            if i == 0:
                rot = R.from_rotvec(single_global_orient)
            else:
                rot = joint_orientations[parents[i]] * R.from_rotvec(
                    single_full_body_pose[i].squeeze()
                )
            joint_orientations.append(rot)
            result[joint_name] = (single_joints[i], rot.as_quat(scalar_first=True))

        smplx_motion_frames.append(result)

    return smplx_motion_frames, aligned_fps


if __name__ == "__main__":
    motion_clip_test = f"/home/<USER>/Documents/Phybot_rl/data/motion_base/MocapRaw/ACCAD/Male1General_c3d/General A1 - Stand_poses.npz"
    smplx_body_model_path = f"/home/<USER>/Documents/Phybot_rl/data/smpl/SMPL_NEUTRAL.pkl"
    body_model = load_smplx_body_model(smplx_body_model_path)
    smplx_clip, body_model, human_pose, human_height = load_smplx_clip(motion_clip_test, body_model)